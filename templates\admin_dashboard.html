{% extends "base.html" %}

{% block title %}Admin Dashboard - Attendance System{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Header -->
    <div class="dashboard-header">
        <div>
            <h1><i class="fas fa-tachometer-alt"></i> Admin Dashboard</h1>
            <p>Welcome, {{ session.full_name }}</p>
        </div>
        <div style="display: flex; gap: 10px;">
            <a href="{{ url_for('admin.course_management') }}" class="btn btn-success">
                <i class="fas fa-chalkboard-teacher"></i> Course Management
            </a>
            <a href="{{ url_for('login.logout') }}" class="btn btn-danger">
                <i class="fas fa-sign-out-alt"></i> Logout
            </a>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <i class="fas fa-users"></i>
            <h3>{{ stats.total_users }}</h3>
            <p>Total Users</p>
        </div>
        <div class="stat-card">
            <i class="fas fa-user-shield"></i>
            <h3>{{ stats.total_admins }}</h3>
            <p>Administrators</p>
        </div>
        <div class="stat-card">
            <i class="fas fa-user-graduate"></i>
            <h3>{{ stats.total_students }}</h3>
            <p>Students</p>
        </div>
        <div class="stat-card">
            <i class="fas fa-book"></i>
            <h3>{{ stats.total_courses }}</h3>
            <p>Total Courses</p>
        </div>
    </div>

    <!-- User Management Section -->
    <div class="dashboard-section">
        <div class="section-header">
            <h2><i class="fas fa-users-cog"></i> User Management</h2>
            {% if is_super_admin %}
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createAdminModal">
                <i class="fas fa-user-shield"></i> Create Admin
            </button>
            {% endif %}
        </div>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Username</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.id }}</td>
                        <td>{{ user.username }}</td>
                        <td>{{ user.email }}</td>
                        <td>
                            <span class="badge bg-{{ 'danger' if user.role == 'admin' else 'success' }}">
                                {{ user.role.title() }}
                                {% if user.is_super_admin %}<i class="fas fa-crown" title="Super Admin"></i>{% endif %}
                            </span>
                        </td>
                        <td>{{ user.created_at[:10] }}</td>
                        <td>
                            {% if user.id != 1 %}
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteUser({{ user.id }})">
                                <i class="fas fa-trash"></i>
                            </button>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Create Admin Modal (Super Admin Only) -->
{% if is_super_admin %}
<div class="modal fade" id="createAdminModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Admin</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('admin.create_admin') }}" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="adminUsername" class="form-label">Username</label>
                        <input type="text" class="form-control" id="adminUsername" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="adminEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="adminEmail" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="adminPassword" class="form-label">Password</label>
                        <input type="password" class="form-control" id="adminPassword" name="password" required>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> New admins will be able to manage courses and students but cannot create other admins.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Admin</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

