import sqlite3
import hashlib
from datetime import datetime

DATABASE = 'attendance.db'

def get_db_connection():
    """Get database connection"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def hash_password(password):
    """Hash password using SHA256"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password, hashed):
    """Verify password against hash"""
    return hashlib.sha256(password.encode()).hexdigest() == hashed

def init_database():
    """Initialize database with tables and default admin user"""
    conn = get_db_connection()
    
    # Create users table with updated schema
    conn.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            full_name TEXT,
            role TEXT NOT NULL DEFAULT 'student',
            is_super_admin INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create courses table
    conn.execute('''
        CREATE TABLE IF NOT EXISTS courses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            course_code TEXT UNIQUE,
            start_date DATE,
            end_date DATE,
            days_per_week INTEGER DEFAULT 1,
            schedule_days TEXT,
            required_attendance_percentage INTEGER DEFAULT 75,
            created_by_admin_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by_admin_id) REFERENCES users (id)
        )
    ''')
    
    # Create course enrollments table
    conn.execute('''
        CREATE TABLE IF NOT EXISTS course_enrollments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            course_id INTEGER NOT NULL,
            student_id INTEGER NOT NULL,
            enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES courses (id) ON DELETE CASCADE,
            FOREIGN KEY (student_id) REFERENCES users (id) ON DELETE CASCADE,
            UNIQUE(course_id, student_id)
        )
    ''')
    
    # Create attendance sessions table (for attendance codes)
    conn.execute('''
        CREATE TABLE IF NOT EXISTS attendance_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            course_id INTEGER NOT NULL,
            session_name TEXT,
            session_date DATE NOT NULL,
            code TEXT NOT NULL,
            code_type TEXT DEFAULT 'manual',
            expires_at TIMESTAMP NOT NULL,
            created_by_admin_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES courses (id) ON DELETE CASCADE,
            FOREIGN KEY (created_by_admin_id) REFERENCES users (id)
        )
    ''')
    
    # Create course attendance table
    conn.execute('''
        CREATE TABLE IF NOT EXISTS course_attendance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id INTEGER NOT NULL,
            student_id INTEGER NOT NULL,
            marked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (session_id) REFERENCES attendance_sessions (id) ON DELETE CASCADE,
            FOREIGN KEY (student_id) REFERENCES users (id) ON DELETE CASCADE,
            UNIQUE(session_id, student_id)
        )
    ''')
    
    # Keep old attendance table for backward compatibility (can be removed later)
    conn.execute('''
        CREATE TABLE IF NOT EXISTS attendance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            date DATE NOT NULL,
            time_in TIME,
            time_out TIME,
            status TEXT DEFAULT 'present',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Check if default admin exists
    admin = conn.execute('SELECT * FROM users WHERE username = ?', ('admin',)).fetchone()
    if not admin:
        # Create default super admin user
        admin_password = hash_password('admin123')
        conn.execute('''
            INSERT INTO users (username, password, email, full_name, role, is_super_admin)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', ('admin', admin_password, '<EMAIL>', 'System Administrator', 'admin', 1))
    else:
        # Update existing admin to be super admin if not already
        conn.execute('''
            UPDATE users SET is_super_admin = 1 WHERE username = ?
        ''', ('admin',))
    
    conn.commit()
    conn.close()

def get_user_by_username(username):
    """Get user by username"""
    conn = get_db_connection()
    user = conn.execute('SELECT * FROM users WHERE username = ?', (username,)).fetchone()
    conn.close()
    return user

def get_user_by_email(email):
    """Get user by email"""
    conn = get_db_connection()
    user = conn.execute('SELECT * FROM users WHERE email = ?', (email,)).fetchone()
    conn.close()
    return user

def get_user_by_id(user_id):
    """Get user by ID"""
    conn = get_db_connection()
    user = conn.execute('SELECT * FROM users WHERE id = ?', (user_id,)).fetchone()
    conn.close()
    return user

def create_user(username, password, email, role='student', full_name=None):
    """Create a new user"""
    conn = get_db_connection()
    hashed_password = hash_password(password)
    
    # If no full_name provided, use username
    if not full_name:
        full_name = username
    
    try:
        conn.execute('''
            INSERT INTO users (username, password, email, full_name, role, is_super_admin)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (username, hashed_password, email, full_name, role, 0))
        conn.commit()
        user_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.close()
        return user_id
    except sqlite3.IntegrityError as e:
        conn.close()
        return None

def get_all_users():
    """Get all users"""
    conn = get_db_connection()
    users = conn.execute('''
        SELECT id, username, email, full_name, role, is_super_admin, created_at 
        FROM users ORDER BY created_at DESC
    ''').fetchall()
    conn.close()
    return users

def get_students():
    """Get all students"""
    conn = get_db_connection()
    students = conn.execute('''
        SELECT id, username, email, full_name, created_at 
        FROM users WHERE role = 'student' ORDER BY full_name
    ''').fetchall()
    conn.close()
    return students

def delete_user(user_id):
    """Delete a user"""
    conn = get_db_connection()
    try:
        conn.execute('DELETE FROM users WHERE id = ?', (user_id,))
        conn.commit()
        conn.close()
        return True
    except sqlite3.Error:
        conn.close()
        return False

# Course management functions
def create_course(name, description, course_code, admin_id, start_date=None, end_date=None,
                  days_per_week=1, schedule_days=None, required_attendance_percentage=75):
    """Create a new course"""
    conn = get_db_connection()
    try:
        conn.execute('''
            INSERT INTO courses (name, description, course_code, start_date, end_date,
                               days_per_week, schedule_days, required_attendance_percentage, created_by_admin_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (name, description, course_code, start_date, end_date, days_per_week,
              schedule_days, required_attendance_percentage, admin_id))
        conn.commit()
        course_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.close()
        return course_id
    except sqlite3.IntegrityError:
        conn.close()
        return None

def get_all_courses():
    """Get all courses"""
    conn = get_db_connection()
    courses = conn.execute('''
        SELECT c.*, u.full_name as created_by_name
        FROM courses c
        JOIN users u ON c.created_by_admin_id = u.id
        ORDER BY c.created_at DESC
    ''').fetchall()
    conn.close()
    return courses

def get_course_by_id(course_id):
    """Get course by ID"""
    conn = get_db_connection()
    course = conn.execute('SELECT * FROM courses WHERE id = ?', (course_id,)).fetchone()
    conn.close()
    return course

def delete_course(course_id):
    """Delete a course"""
    conn = get_db_connection()
    try:
        conn.execute('DELETE FROM courses WHERE id = ?', (course_id,))
        conn.commit()
        conn.close()
        return True
    except sqlite3.Error:
        conn.close()
        return False

# Enrollment functions
def enroll_student(course_id, student_id):
    """Enroll a student in a course"""
    conn = get_db_connection()
    try:
        conn.execute('''
            INSERT INTO course_enrollments (course_id, student_id)
            VALUES (?, ?)
        ''', (course_id, student_id))
        conn.commit()
        conn.close()
        return True
    except sqlite3.IntegrityError:
        conn.close()
        return False

def unenroll_student(course_id, student_id):
    """Unenroll a student from a course"""
    conn = get_db_connection()
    try:
        conn.execute('''
            DELETE FROM course_enrollments 
            WHERE course_id = ? AND student_id = ?
        ''', (course_id, student_id))
        conn.commit()
        conn.close()
        return True
    except sqlite3.Error:
        conn.close()
        return False

def get_course_students(course_id):
    """Get all students enrolled in a course"""
    conn = get_db_connection()
    students = conn.execute('''
        SELECT u.id, u.username, u.email, u.full_name, ce.enrolled_at
        FROM users u
        JOIN course_enrollments ce ON u.id = ce.student_id
        WHERE ce.course_id = ?
        ORDER BY u.full_name
    ''', (course_id,)).fetchall()
    conn.close()
    return students

def get_student_courses(student_id):
    """Get all courses a student is enrolled in"""
    conn = get_db_connection()
    courses = conn.execute('''
        SELECT c.*, ce.enrolled_at
        FROM courses c
        JOIN course_enrollments ce ON c.id = ce.course_id
        WHERE ce.student_id = ?
        ORDER BY c.name
    ''', (student_id,)).fetchall()
    conn.close()
    return courses

# Attendance session functions
def create_attendance_session(course_id, session_name, session_date, code, code_type, expires_at, admin_id):
    """Create an attendance session with code"""
    conn = get_db_connection()
    try:
        conn.execute('''
            INSERT INTO attendance_sessions
            (course_id, session_name, session_date, code, code_type, expires_at, created_by_admin_id)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (course_id, session_name, session_date, code, code_type, expires_at, admin_id))
        conn.commit()
        session_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
        conn.close()
        return session_id
    except sqlite3.Error:
        conn.close()
        return None

def get_attendance_session_by_code(code):
    """Get attendance session by code"""
    conn = get_db_connection()
    session = conn.execute('''
        SELECT * FROM attendance_sessions WHERE code = ?
    ''', (code,)).fetchone()
    conn.close()
    return session

def get_course_sessions(course_id):
    """Get all attendance sessions for a course"""
    conn = get_db_connection()
    sessions = conn.execute('''
        SELECT * FROM attendance_sessions
        WHERE course_id = ?
        ORDER BY session_date DESC, created_at DESC
    ''', (course_id,)).fetchall()
    conn.close()
    return sessions

def mark_attendance(session_id, student_id):
    """Mark student attendance for a session"""
    conn = get_db_connection()
    try:
        conn.execute('''
            INSERT INTO course_attendance (session_id, student_id)
            VALUES (?, ?)
        ''', (session_id, student_id))
        conn.commit()
        conn.close()
        return True
    except sqlite3.IntegrityError:
        # Already marked
        conn.close()
        return False

def get_session_attendance(session_id):
    """Get all attendance records for a session"""
    conn = get_db_connection()
    records = conn.execute('''
        SELECT ca.*, u.username, u.full_name, u.email
        FROM course_attendance ca
        JOIN users u ON ca.student_id = u.id
        WHERE ca.session_id = ?
        ORDER BY ca.marked_at
    ''', (session_id,)).fetchall()
    conn.close()
    return records

def get_course_attendance_by_date(course_id, date):
    """Get attendance for a course on a specific date"""
    conn = get_db_connection()
    records = conn.execute('''
        SELECT ca.*, u.username, u.full_name, u.email, ats.session_name, ats.session_date
        FROM course_attendance ca
        JOIN users u ON ca.student_id = u.id
        JOIN attendance_sessions ats ON ca.session_id = ats.id
        WHERE ats.course_id = ? AND ats.session_date = ?
        ORDER BY ca.marked_at
    ''', (course_id, date)).fetchall()
    conn.close()
    return records

def is_student_enrolled(course_id, student_id):
    """Check if student is enrolled in a course"""
    conn = get_db_connection()
    enrollment = conn.execute('''
        SELECT * FROM course_enrollments
        WHERE course_id = ? AND student_id = ?
    ''', (course_id, student_id)).fetchone()
    conn.close()
    return enrollment is not None

def get_student_statistics(student_id):
    """Get overall statistics for a student"""
    conn = get_db_connection()

    # Get total enrolled courses
    enrolled_courses = conn.execute('''
        SELECT COUNT(*) as count FROM course_enrollments WHERE student_id = ?
    ''', (student_id,)).fetchone()['count']

    # Get total sessions attended
    total_attended = conn.execute('''
        SELECT COUNT(*) as count FROM course_attendance WHERE student_id = ?
    ''', (student_id,)).fetchone()['count']

    conn.close()
    return {
        'enrolled_courses': enrolled_courses,
        'total_attended': total_attended
    }

def get_student_course_statistics(student_id, course_id):
    """Get statistics for a student in a specific course"""
    conn = get_db_connection()

    # Get course details
    course = conn.execute('SELECT * FROM courses WHERE id = ?', (course_id,)).fetchone()

    # Get total sessions for this course
    total_sessions = conn.execute('''
        SELECT COUNT(*) as count FROM attendance_sessions WHERE course_id = ?
    ''', (course_id,)).fetchone()['count']

    # Get sessions attended by student
    attended_sessions = conn.execute('''
        SELECT COUNT(*) as count
        FROM course_attendance ca
        JOIN attendance_sessions ats ON ca.session_id = ats.id
        WHERE ca.student_id = ? AND ats.course_id = ?
    ''', (student_id, course_id)).fetchone()['count']

    # Calculate percentage
    attendance_percentage = (attended_sessions / total_sessions * 100) if total_sessions > 0 else 0

    conn.close()
    return {
        'course_name': course['name'],
        'total_sessions': total_sessions,
        'attended_sessions': attended_sessions,
        'attendance_percentage': round(attendance_percentage, 2),
        'required_percentage': course['required_attendance_percentage']
    }

def get_all_students_with_stats():
    """Get all students with their statistics"""
    conn = get_db_connection()
    students = conn.execute('''
        SELECT id, username, email, full_name, created_at
        FROM users WHERE role = 'student' ORDER BY full_name
    ''').fetchall()

    student_list = []
    for student in students:
        # Get enrolled courses count
        enrolled_count = conn.execute('''
            SELECT COUNT(*) as count FROM course_enrollments WHERE student_id = ?
        ''', (student['id'],)).fetchone()['count']

        # Get total attendance count
        attendance_count = conn.execute('''
            SELECT COUNT(*) as count FROM course_attendance WHERE student_id = ?
        ''', (student['id'],)).fetchone()['count']

        student_list.append({
            'id': student['id'],
            'username': student['username'],
            'email': student['email'],
            'full_name': student['full_name'],
            'enrolled_courses': enrolled_count,
            'total_attended': attendance_count
        })

    conn.close()
    return student_list

