{% extends "base.html" %}

{% block title %}Course Management - Attendance System{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Header -->
    <div class="dashboard-header">
        <div>
            <h1><i class="fas fa-chalkboard-teacher"></i> Course Management</h1>
            <p>Manage courses, students, and attendance</p>
        </div>
        <div style="display: flex; gap: 10px;">
            <a href="{{ url_for('admin.attendance_view') }}" class="btn btn-info">
                <i class="fas fa-chart-bar"></i> View Attendance
            </a>
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
            <a href="{{ url_for('login.logout') }}" class="btn btn-danger">
                <i class="fas fa-sign-out-alt"></i> Logout
            </a>
        </div>
    </div>

    <!-- Create Course Button -->
    <div style="margin-bottom: 20px;">
        <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#createCourseModal">
            <i class="fas fa-plus"></i> Create New Course
        </button>
    </div>

    <!-- Courses List -->
    {% if course_data %}
        {% for item in course_data %}
        <div class="dashboard-section">
            <div class="section-header">
                <div>
                    <h2><i class="fas fa-book"></i> {{ item.course.name }} ({{ item.course.course_code }})</h2>
                    <p style="margin: 0; opacity: 0.9;">{{ item.course.description }}</p>
                    {% if item.course.start_date %}
                    <small style="opacity: 0.8;">
                        <i class="fas fa-calendar"></i> {{ item.course.start_date }} to {{ item.course.end_date or 'Ongoing' }}
                        {% if item.course.schedule_days %}
                        | <i class="fas fa-clock"></i> {{ item.course.days_per_week }} days/week ({{ item.course.schedule_days }})
                        {% endif %}
                        | <i class="fas fa-percentage"></i> Required: {{ item.course.required_attendance_percentage }}%
                    </small><br>
                    {% endif %}
                    <small style="opacity: 0.8;">Created by: {{ item.course.created_by_name }}</small>
                </div>
                <div style="display: flex; gap: 10px;">
                    <a href="{{ url_for('admin.course_sessions', course_id=item.course.id) }}" class="btn btn-success">
                        <i class="fas fa-qrcode"></i> Manage Sessions
                    </a>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#enrollModal{{ item.course.id }}">
                        <i class="fas fa-user-plus"></i> Enroll Student
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteCourse({{ item.course.id }})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            
            <div style="padding: 20px;">
                <h5>Enrolled Students ({{ item.enrolled_count }})</h5>
                {% if item.enrolled_students %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Enrolled Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in item.enrolled_students %}
                            <tr>
                                <td>
                                    <a href="#" onclick="showStudentStats({{ student.id }}); return false;"
                                       style="text-decoration: none; color: #0d6efd;">
                                        <i class="fas fa-user"></i> {{ student.full_name }}
                                    </a>
                                </td>
                                <td>{{ student.username }}</td>
                                <td>{{ student.email }}</td>
                                <td>{{ student.enrolled_at[:10] }}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-danger"
                                            onclick="unenrollStudent({{ item.course.id }}, {{ student.id }})">
                                        <i class="fas fa-user-minus"></i> Unenroll
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">No students enrolled yet.</p>
                {% endif %}
            </div>
        </div>

        <!-- Enroll Student Modal for this course -->
        <div class="modal fade" id="enrollModal{{ item.course.id }}" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Enroll Student in {{ item.course.name }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form action="{{ url_for('admin.enroll_student_route') }}" method="POST">
                        <input type="hidden" name="course_id" value="{{ item.course.id }}">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="studentSearch{{ item.course.id }}" class="form-label">Search Student</label>
                                <input type="text" class="form-control mb-2" id="studentSearch{{ item.course.id }}"
                                       placeholder="Type to search by name or username..."
                                       onkeyup="filterStudents({{ item.course.id }})">
                            </div>
                            <div class="mb-3">
                                <label for="studentSelect{{ item.course.id }}" class="form-label">Select Student</label>
                                <select class="form-control" id="studentSelect{{ item.course.id }}" name="student_id"
                                        size="8" required style="height: 200px;">
                                    {% for student in students %}
                                    <option value="{{ student.id }}"
                                            data-name="{{ student.full_name|lower }}"
                                            data-username="{{ student.username|lower }}"
                                            title="Enrolled: {{ student.enrolled_courses }} courses | Attended: {{ student.total_attended }} sessions">
                                        {{ student.full_name }} ({{ student.username }}) - {{ student.enrolled_courses }} courses
                                    </option>
                                    {% endfor %}
                                </select>
                                <small class="text-muted">Showing enrolled courses and total attendance</small>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">Enroll Student</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> No courses created yet. Click "Create New Course" to get started.
        </div>
    {% endif %}
</div>

<!-- Create Course Modal -->
<div class="modal fade" id="createCourseModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Course</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('admin.create_course_route') }}" method="POST">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="courseName" class="form-label">Course Name *</label>
                            <input type="text" class="form-control" id="courseName" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="courseCode" class="form-label">Course Code *</label>
                            <input type="text" class="form-control" id="courseCode" name="course_code"
                                   placeholder="e.g., CS101" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="courseDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="courseDescription" name="description" rows="2"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="startDate" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="startDate" name="start_date">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="endDate" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="endDate" name="end_date">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="daysPerWeek" class="form-label">Classes Per Week</label>
                            <input type="number" class="form-control" id="daysPerWeek" name="days_per_week"
                                   min="1" max="7" value="1">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="requiredAttendance" class="form-label">Required Attendance %</label>
                            <input type="number" class="form-control" id="requiredAttendance" name="required_attendance"
                                   min="0" max="100" value="75">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Schedule Days</label>
                        <div class="d-flex flex-wrap gap-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="schedule_days" value="Monday" id="mon">
                                <label class="form-check-label" for="mon">Mon</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="schedule_days" value="Tuesday" id="tue">
                                <label class="form-check-label" for="tue">Tue</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="schedule_days" value="Wednesday" id="wed">
                                <label class="form-check-label" for="wed">Wed</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="schedule_days" value="Thursday" id="thu">
                                <label class="form-check-label" for="thu">Thu</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="schedule_days" value="Friday" id="fri">
                                <label class="form-check-label" for="fri">Fri</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="schedule_days" value="Saturday" id="sat">
                                <label class="form-check-label" for="sat">Sat</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="schedule_days" value="Sunday" id="sun">
                                <label class="form-check-label" for="sun">Sun</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Course</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Student Stats Modal -->
<div class="modal fade" id="studentStatsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-chart-line"></i> Student Statistics</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="studentStatsContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deleteCourse(courseId) {
    if (confirm('Are you sure you want to delete this course? This will also delete all enrollments and attendance sessions.')) {
        fetch('/admin/delete-course', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({ course_id: courseId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
}

function unenrollStudent(courseId, studentId) {
    if (confirm('Are you sure you want to unenroll this student?')) {
        fetch('/admin/unenroll-student', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({ course_id: courseId, student_id: studentId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
}

function filterStudents(courseId) {
    const searchInput = document.getElementById('studentSearch' + courseId);
    const select = document.getElementById('studentSelect' + courseId);
    const filter = searchInput.value.toLowerCase();
    const options = select.options;

    for (let i = 0; i < options.length; i++) {
        const name = options[i].getAttribute('data-name') || '';
        const username = options[i].getAttribute('data-username') || '';

        if (name.includes(filter) || username.includes(filter)) {
            options[i].style.display = '';
        } else {
            options[i].style.display = 'none';
        }
    }
}

function showStudentStats(studentId) {
    const modal = new bootstrap.Modal(document.getElementById('studentStatsModal'));
    const content = document.getElementById('studentStatsContent');

    // Show loading
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    `;

    modal.show();

    // Fetch student stats
    fetch('/admin/student-stats/' + studentId)
        .then(response => response.json())
        .then(data => {
            let html = `
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-primary">${data.enrolled_courses}</h3>
                                <p class="mb-0">Enrolled Courses</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-success">${data.total_attended}</h3>
                                <p class="mb-0">Total Sessions Attended</p>
                            </div>
                        </div>
                    </div>
                </div>
                <h5>Course-wise Attendance</h5>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Course</th>
                                <th>Sessions</th>
                                <th>Attended</th>
                                <th>Percentage</th>
                                <th>Required</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            if (data.courses && data.courses.length > 0) {
                data.courses.forEach(course => {
                    const percentage = course.attendance_percentage;
                    const required = course.required_percentage;
                    const status = percentage >= required ?
                        '<span class="badge bg-success">Good</span>' :
                        '<span class="badge bg-danger">Low</span>';

                    html += `
                        <tr>
                            <td>${course.course_name}</td>
                            <td>${course.total_sessions}</td>
                            <td>${course.attended_sessions}</td>
                            <td>${percentage.toFixed(1)}%</td>
                            <td>${required}%</td>
                            <td>${status}</td>
                        </tr>
                    `;
                });
            } else {
                html += '<tr><td colspan="6" class="text-center text-muted">No attendance data yet</td></tr>';
            }

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            content.innerHTML = html;
        })
        .catch(error => {
            content.innerHTML = '<div class="alert alert-danger">Error loading student statistics</div>';
        });
}
</script>
{% endblock %}

