{% extends "base.html" %}

{% block title %}Attendance View - Attendance System{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Header -->
    <div class="dashboard-header">
        <div>
            <h1><i class="fas fa-chart-bar"></i> Attendance View</h1>
            <p>Filter and view attendance records</p>
        </div>
        <div style="display: flex; gap: 10px;">
            <a href="{{ url_for('admin.course_management') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Courses
            </a>
            <a href="{{ url_for('login.logout') }}" class="btn btn-danger">
                <i class="fas fa-sign-out-alt"></i> Logout
            </a>
        </div>
    </div>

    <!-- Filter Form -->
    <div class="dashboard-section">
        <div class="section-header">
            <h2><i class="fas fa-filter"></i> Filter Attendance</h2>
        </div>
        <div style="padding: 20px;">
            <form method="GET" action="{{ url_for('admin.attendance_view') }}">
                <div class="row">
                    <div class="col-md-5">
                        <label for="courseSelect" class="form-label">Select Course</label>
                        <select class="form-control" id="courseSelect" name="course_id" required>
                            <option value="">Choose a course...</option>
                            {% for course in courses %}
                            <option value="{{ course.id }}" {% if selected_course == course.id %}selected{% endif %}>
                                {{ course.name }} ({{ course.course_code }})
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label for="dateSelect" class="form-label">Select Date</label>
                        <input type="date" class="form-control" id="dateSelect" name="date" 
                               value="{{ selected_date }}" required>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i> View
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Attendance Records -->
    {% if selected_course and selected_date %}
    <div class="dashboard-section">
        <div class="section-header">
            <h2><i class="fas fa-list"></i> Attendance Records</h2>
            <span class="badge bg-success" style="font-size: 1.1rem; padding: 10px 15px;">
                {{ attendance_records|length }} students attended
            </span>
        </div>
        <div style="padding: 20px;">
            {% if attendance_records %}
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Student Name</th>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Session Name</th>
                            <th>Marked At</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for record in attendance_records %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ record.full_name }}</td>
                            <td>{{ record.username }}</td>
                            <td>{{ record.email }}</td>
                            <td>{{ record.session_name }}</td>
                            <td>{{ record.marked_at }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> No attendance records found for the selected course and date.
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<script>
// Set today's date as default
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('dateSelect');
    if (!dateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.value = today;
    }
});
</script>
{% endblock %}

