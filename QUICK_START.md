# Quick Start Guide

## 🚀 Getting Started in 5 Minutes

### Step 1: Run the Application
```bash
python app.py
```

The application will start at: **http://127.0.0.1:5000**

---

## 🔐 Default Login Credentials

**Super Admin Account:**
- Username: `admin`
- Email: `<EMAIL>`
- Password: `admin123`

---

## 📋 Quick Walkthrough

### For Administrators

#### 1️⃣ First Login
- Open http://127.0.0.1:5000
- Login with admin credentials above
- You'll see the Admin Dashboard

#### 2️⃣ Create a Course
- Click **"Course Management"** button (top right)
- Click **"Create New Course"**
- Fill in:
  - Course Name: e.g., "Introduction to Python"
  - Course Code: e.g., "CS101"
  - Description: (optional)
- Click **"Create Course"**

#### 3️⃣ Create Student Accounts
Option A: Students self-register
- Share the registration link with students
- They register with username, email, password

Option B: Admin creates accounts
- On Admin Dashboard, you can create users manually

#### 4️⃣ Enroll Students in Course
- Go to **Course Management**
- Find your course
- Click **"Enroll Student"**
- Select student from dropdown
- Click **"Enroll Student"**

#### 5️⃣ Generate Attendance Code
- Click **"Manage Sessions"** for your course
- Click **"Generate Attendance Code"**
- Fill in:
  - Session Name: e.g., "Lecture 1"
  - Session Date: Select date
  - Code Type: Choose "Auto-generate" or "Manual"
  - Duration: e.g., 60 minutes
- Click **"Generate Code"**
- **Share the code with your students!**

#### 6️⃣ View Attendance
- Click **"View Attendance"** from Course Management
- Select your course
- Select the date
- Click **"View"**
- See list of students who attended

---

### For Students

#### 1️⃣ Register
- Go to http://127.0.0.1:5000
- Click **"Sign up here"**
- Enter:
  - Username
  - Email
  - Password
  - Confirm Password
- Click **"Create Account"**

#### 2️⃣ Login
- Enter your username (or email) and password
- Click **"Sign In"**

#### 3️⃣ View Your Courses
- After login, you'll see your Student Dashboard
- View all courses you're enrolled in
- (Admin must enroll you first)

#### 4️⃣ Mark Attendance
- Click **"Mark Attendance"** or **"Enter Attendance Code"**
- Enter the code your instructor gave you
- Click **"Submit Code"**
- You'll see a success message!

---

## 🎯 Common Scenarios

### Scenario 1: Running a Class Session
1. Admin generates attendance code before class
2. Admin shares code with students (write on board, show on screen, etc.)
3. Students enter code on their devices
4. Admin can view attendance in real-time

### Scenario 2: Creating Additional Admins
1. Login as super admin (default admin)
2. On Admin Dashboard, click **"Create Admin"**
3. Enter new admin's username, email, password
4. New admin can now manage courses but cannot create more admins

### Scenario 3: Checking Who Attended
1. Go to Course Management
2. Click **"View Attendance"**
3. Select course and date
4. See complete list with timestamps

---

## 💡 Tips & Tricks

### For Admins:
- **Auto-generate codes** for quick setup
- **Manual codes** for memorable/themed codes (e.g., "PYTHON", "CS101A")
- Set **longer durations** for lab sessions (e.g., 120 minutes)
- Set **shorter durations** for lectures (e.g., 30 minutes)
- Check attendance immediately after class ends

### For Students:
- **Save the attendance page** as a bookmark for quick access
- Codes are **case-insensitive** (ABC123 = abc123)
- You can only mark attendance **once per session**
- Codes **expire** - don't wait too long!

---

## 🔧 Troubleshooting

### "Invalid attendance code"
- Check if you typed the code correctly
- Codes are case-insensitive but must match exactly
- Ask your instructor for the correct code

### "This attendance code has expired"
- The time limit has passed
- Contact your instructor

### "You are not enrolled in this course"
- You need to be enrolled by an admin first
- Contact your instructor or admin

### "You have already marked attendance for this session"
- You can only mark attendance once per session
- Your attendance is already recorded

### Cannot create admins
- Only the super admin (default admin) can create new admins
- Regular admins do not have this privilege

---

## 📊 System Features Summary

✅ Login with username OR email  
✅ Simplified registration (no full name needed)  
✅ Super admin can create regular admins  
✅ Course creation and management  
✅ Student enrollment system  
✅ Auto and manual attendance code generation  
✅ Time-limited attendance codes  
✅ Real-time attendance tracking  
✅ Attendance filtering by course and date  
✅ Clean, organized codebase  

---

## 📁 File Structure

```
login/          - Login and registration
admin/          - Admin features and course management
student/        - Student dashboard and attendance marking
templates/      - Shared templates (base.html)
static/         - CSS and JavaScript
database.py     - All database functions
app.py          - Main application (minimal)
```

---

## 🆘 Need Help?

1. Check the **README.md** for detailed documentation
2. Check **CHANGES_SUMMARY.md** for what was changed
3. Review the code comments in each file
4. Check the browser console for JavaScript errors
5. Check the terminal for Python errors

---

## 🎓 Example Class Flow

**Before Class:**
1. Admin creates course "Python 101"
2. Admin enrolls all students
3. Admin generates code "ABC123" valid for 60 minutes

**During Class:**
1. Admin writes "ABC123" on board
2. Students open attendance page
3. Students enter "ABC123"
4. Students receive confirmation

**After Class:**
1. Admin views attendance report
2. Admin sees 25/30 students attended
3. Admin can export or review data

---

## 🚀 You're Ready!

The system is now fully functional. Start by:
1. Running `python app.py`
2. Logging in as admin
3. Creating your first course
4. Enrolling students
5. Generating your first attendance code

**Happy Teaching! 📚**

