from flask import Blueprint, render_template, request, redirect, url_for, session, flash, jsonify
from datetime import datetime
from database import (
    get_student_courses, get_attendance_session_by_code,
    mark_attendance, is_student_enrolled, get_course_by_id,
    get_student_course_statistics
)

student_bp = Blueprint('student', __name__)

def student_required(f):
    """Decorator to require student access"""
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please login to access your dashboard.', 'error')
            return redirect(url_for('login.login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@student_bp.route('/student')
@student_required
def dashboard():
    """Student dashboard showing enrolled courses"""
    student_id = session['user_id']
    courses = get_student_courses(student_id)
    
    return render_template('student_dashboard.html', courses=courses)

@student_bp.route('/student/mark-attendance', methods=['GET', 'POST'])
@student_required
def mark_attendance_page():
    """Page for students to enter attendance code"""
    if request.method == 'POST':
        code = request.form['code'].upper().strip()
        student_id = session['user_id']
        
        # Find the session by code
        attendance_session = get_attendance_session_by_code(code)
        
        if not attendance_session:
            flash('Invalid attendance code!', 'error')
            return render_template('mark_attendance.html')
        
        # Check if code has expired
        expires_at = datetime.strptime(attendance_session['expires_at'], '%Y-%m-%d %H:%M:%S.%f')
        if datetime.now() > expires_at:
            flash('This attendance code has expired!', 'error')
            return render_template('mark_attendance.html')

        # Check if student is enrolled in the course
        if not is_student_enrolled(attendance_session['course_id'], student_id):
            flash('You are not enrolled in this course!', 'error')
            return render_template('mark_attendance.html')
        
        # Mark attendance
        if mark_attendance(attendance_session['id'], student_id):
            course = get_course_by_id(attendance_session['course_id'])
            flash(f'Attendance marked successfully for {course["name"]}!', 'success')
            return redirect(url_for('student.dashboard'))
        else:
            flash('You have already marked attendance for this session!', 'error')
    
    return render_template('mark_attendance.html')

@student_bp.route('/student/course-stats/<int:course_id>')
@student_required
def get_course_stats(course_id):
    """Get statistics for a specific course"""
    student_id = session['user_id']
    stats = get_student_course_statistics(student_id, course_id)
    return jsonify(stats)
