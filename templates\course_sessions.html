{% extends "base.html" %}

{% block title %}{{ course.name }} - Sessions{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Header -->
    <div class="dashboard-header">
        <div>
            <h1><i class="fas fa-qrcode"></i> {{ course.name }} - Attendance Sessions</h1>
            <p>Course Code: {{ course.course_code }}</p>
        </div>
        <div style="display: flex; gap: 10px;">
            <a href="{{ url_for('admin.course_management') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Courses
            </a>
            <a href="{{ url_for('login.logout') }}" class="btn btn-danger">
                <i class="fas fa-sign-out-alt"></i> Logout
            </a>
        </div>
    </div>

    <!-- Create Session Button -->
    <div style="margin-bottom: 20px;">
        <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#createSessionModal">
            <i class="fas fa-plus"></i> Generate Attendance Code
        </button>
    </div>

    <!-- Sessions List -->
    {% if session_data %}
        {% for item in session_data %}
        <div class="dashboard-section">
            <div class="section-header">
                <div>
                    <h2><i class="fas fa-calendar-day"></i> {{ item.session.session_name }}</h2>
                    <p style="margin: 0; opacity: 0.9;">
                        Date: {{ item.session.session_date }} | 
                        Code: <strong>{{ item.session.code }}</strong> | 
                        Expires: {{ item.session.expires_at }}
                    </p>
                </div>
                <div>
                    <span class="badge bg-info" style="font-size: 1.2rem; padding: 10px 15px;">
                        {{ item.attendance_count }} / {{ enrolled_students|length }} attended
                    </span>
                </div>
            </div>
            
            <div style="padding: 20px;">
                {% if item.attendance_records %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Student Name</th>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Marked At</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in item.attendance_records %}
                            <tr>
                                <td>{{ record.full_name }}</td>
                                <td>{{ record.username }}</td>
                                <td>{{ record.email }}</td>
                                <td>{{ record.marked_at }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">No students have marked attendance yet.</p>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> No attendance sessions created yet. Click "Generate Attendance Code" to create one.
        </div>
    {% endif %}
</div>

<!-- Create Session Modal -->
<div class="modal fade" id="createSessionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Generate Attendance Code</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('admin.create_session') }}" method="POST">
                <input type="hidden" name="course_id" value="{{ course.id }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="sessionName" class="form-label">Session Name</label>
                        <input type="text" class="form-control" id="sessionName" name="session_name" 
                               placeholder="e.g., Lecture 1, Lab Session 3" required>
                    </div>
                    <div class="mb-3">
                        <label for="sessionDate" class="form-label">Session Date</label>
                        <input type="date" class="form-control" id="sessionDate" name="session_date" required>
                    </div>
                    <div class="mb-3">
                        <label for="codeType" class="form-label">Code Type</label>
                        <select class="form-control" id="codeType" name="code_type" onchange="toggleCodeInput()" required>
                            <option value="auto">Auto-generate</option>
                            <option value="manual">Manual</option>
                        </select>
                    </div>
                    <div class="mb-3" id="manualCodeDiv" style="display: none;">
                        <label for="manualCode" class="form-label">Enter Code</label>
                        <input type="text" class="form-control" id="manualCode" name="manual_code" 
                               placeholder="Enter custom code">
                    </div>
                    <div class="mb-3">
                        <label for="duration" class="form-label">Code Valid For (minutes)</label>
                        <input type="number" class="form-control" id="duration" name="duration_minutes" 
                               value="60" min="5" max="1440" required>
                        <small class="text-muted">Code will expire after this duration</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Generate Code</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleCodeInput() {
    const codeType = document.getElementById('codeType').value;
    const manualCodeDiv = document.getElementById('manualCodeDiv');
    const manualCodeInput = document.getElementById('manualCode');
    
    if (codeType === 'manual') {
        manualCodeDiv.style.display = 'block';
        manualCodeInput.required = true;
    } else {
        manualCodeDiv.style.display = 'none';
        manualCodeInput.required = false;
    }
}

// Set today's date as default
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('sessionDate').value = today;
});
</script>
{% endblock %}

