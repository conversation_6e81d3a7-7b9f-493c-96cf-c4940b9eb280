# Attendance Management System

A comprehensive Flask-based attendance management system with role-based access control, course management, and automated attendance tracking using time-limited codes.

## Features

### Authentication System
- **Improved Login**: Users can log in using either username or email with their password
- **Simplified Registration**: No need to enter full name separately - username is used as display name
- **Secure Password Hashing**: SHA-256 password encryption

### Admin Features

#### Super Admin (Default Admin)
- **Create New Admins**: Only super admin can create additional administrators
- **Full System Access**: Complete control over all features

#### Regular Admin
- **Course Management**: Create, view, and delete courses
- **Student Enrollment**: Manually add/remove students from courses
- **Attendance Code Generation**: 
  - Auto-generate random 6-character codes
  - Create custom manual codes
  - Set expiration time (5-1440 minutes)
- **Attendance Tracking**: View attendance by course and date
- **User Management**: View all users in the system

### Student Features
- **Course Dashboard**: View all enrolled courses
- **Mark Attendance**: Enter attendance codes to mark presence
- **Real-time Validation**: 
  - Code expiration checking
  - Enrollment verification
  - Duplicate attendance prevention

## Project Structure

```
attendance-application/
├── app.py                      # Main Flask application
├── database.py                 # Database functions and schema
├── requirements.txt            # Python dependencies
│
├── login/                      # Login module
│   ├── routes.py              # Login/register/logout routes
│   ├── login.html             # Login page template
│   └── register.html          # Registration page template
│
├── admin/                      # Admin module
│   ├── routes.py              # Admin routes and logic
│   ├── admin_dashboard.html   # Main admin dashboard
│   ├── course_management.html # Course and enrollment management
│   ├── course_sessions.html   # Attendance session management
│   └── attendance_view.html   # Attendance viewing with filters
│
├── student/                    # Student module
│   ├── routes.py              # Student routes
│   ├── student_dashboard.html # Student dashboard
│   └── mark_attendance.html   # Attendance marking page
│
├── templates/                  # Shared templates
│   └── base.html              # Base template
│
└── static/                     # Static files
    ├── style.css              # Styles
    └── script.js              # JavaScript functions
```

## Database Schema

### Tables

1. **users**
   - id, username, password, email, full_name, role, is_super_admin, created_at

2. **courses**
   - id, name, description, course_code, created_by_admin_id, created_at

3. **course_enrollments**
   - id, course_id, student_id, enrolled_at

4. **attendance_sessions**
   - id, course_id, session_name, session_date, code, code_type, expires_at, created_by_admin_id, created_at

5. **course_attendance**
   - id, session_id, student_id, marked_at

## Installation

1. Install dependencies:
```bash
pip install flask
```

2. Run the application:
```bash
python app.py
```

3. Access the application at `http://127.0.0.1:5000`

## Default Credentials

**Super Admin:**
- Username: `admin`
- Email: `<EMAIL>`
- Password: `admin123`

## Usage Guide

### For Super Admin

1. **Login** with default credentials
2. **Create Admins** (optional):
   - Click "Create Admin" button on dashboard
   - Enter username, email, and password
   - New admins can manage courses but cannot create other admins

3. **Access Course Management**:
   - Click "Course Management" button in top right

### For Admin (Super Admin or Regular Admin)

1. **Create a Course**:
   - Go to Course Management page
   - Click "Create New Course"
   - Enter course name, code, and description

2. **Enroll Students**:
   - Find the course in the list
   - Click "Enroll Student"
   - Select student from dropdown
   - Click "Enroll Student"

3. **Generate Attendance Code**:
   - Click "Manage Sessions" for a course
   - Click "Generate Attendance Code"
   - Fill in session details:
     - Session name (e.g., "Lecture 1")
     - Session date
     - Code type (Auto or Manual)
     - Duration (how long code is valid)
   - Share the generated code with students

4. **View Attendance**:
   - Click "View Attendance" from Course Management
   - Select course and date
   - View list of students who attended

### For Students

1. **Register an Account**:
   - Click "Sign up here" on login page
   - Enter username, email, and password
   - Login after registration

2. **View Enrolled Courses**:
   - After login, see dashboard with enrolled courses
   - (Admin must enroll you first)

3. **Mark Attendance**:
   - Click "Mark Attendance" or "Enter Attendance Code"
   - Enter the code provided by instructor
   - Submit to mark attendance
   - Receive confirmation message

## Key Improvements Implemented

### Task 1: Login System Improvements ✅
- ✅ Removed full_name field from registration form
- ✅ Username is used as display name automatically
- ✅ Login accepts both username and email
- ✅ Email field is now unique in database

### Task 2: Admin Features ✅
- ✅ Super admin can create new admins
- ✅ Regular admins cannot create other admins
- ✅ "Course Management" button in top right of admin dashboard
- ✅ Complete course management system
- ✅ Student enrollment management
- ✅ Attendance code generation (manual and automatic)
- ✅ Code expiration system
- ✅ Attendance viewing with filters by course and date

### Task 3: Code Organization ✅
- ✅ Created `login/` folder with login-related code
- ✅ Created `admin/` folder with admin-related code
- ✅ Created `student/` folder with student-related code
- ✅ Minimized number of files using Flask Blueprints
- ✅ Clean, modular structure

## Technical Details

### Flask Blueprints
The application uses Flask Blueprints for modular organization:
- `login_bp`: Handles authentication
- `admin_bp`: Handles admin functionality
- `student_bp`: Handles student functionality

### Security Features
- Password hashing with SHA-256
- Session-based authentication
- Role-based access control
- Super admin privilege checking
- Code expiration validation
- Enrollment verification

### Database Design
- Relational database with foreign keys
- Cascade deletion for data integrity
- Unique constraints to prevent duplicates
- Timestamp tracking for all records

## Future Enhancements (Optional)

- Email notifications for attendance codes
- QR code generation for attendance
- Attendance reports and analytics
- Export attendance to CSV/Excel
- Student attendance history view
- Multi-semester support
- Attendance percentage calculation

## Support

For issues or questions, please refer to the code comments or contact the system administrator.

---

**Version**: 2.0  
**Last Updated**: 2025  
**Framework**: Flask  
**Database**: SQLite

