from flask import Blueprint, render_template, request, redirect, url_for, session, flash, jsonify
from datetime import datetime, timedelta, date
import random
import string
from database import (
    get_db_connection, get_all_users, create_user, delete_user,
    get_all_courses, create_course, get_course_by_id, delete_course,
    get_students, get_course_students, enroll_student, unenroll_student,
    create_attendance_session, get_course_sessions, get_session_attendance,
    get_course_attendance_by_date, get_attendance_session_by_code,
    get_student_statistics, get_student_course_statistics, get_all_students_with_stats
)

admin_bp = Blueprint('admin', __name__)

def admin_required(f):
    """Decorator to require admin access"""
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session or session.get('role') != 'admin':
            flash('Access denied! Admin privileges required.', 'error')
            return redirect(url_for('login.login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

def super_admin_required(f):
    """Decorator to require super admin access"""
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session or session.get('role') != 'admin' or not session.get('is_super_admin'):
            flash('Access denied! Super admin privileges required.', 'error')
            return redirect(url_for('admin.dashboard'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@admin_bp.route('/admin')
@admin_required
def dashboard():
    """Admin dashboard with statistics"""
    conn = get_db_connection()
    
    # Get statistics
    total_users = conn.execute('SELECT COUNT(*) as count FROM users').fetchone()['count']
    total_admins = conn.execute('SELECT COUNT(*) as count FROM users WHERE role = "admin"').fetchone()['count']
    total_students = conn.execute('SELECT COUNT(*) as count FROM users WHERE role = "student"').fetchone()['count']
    total_courses = conn.execute('SELECT COUNT(*) as count FROM courses').fetchone()['count']
    
    # Get all users
    users = get_all_users()
    
    conn.close()
    
    stats = {
        'total_users': total_users,
        'total_admins': total_admins,
        'total_students': total_students,
        'total_courses': total_courses
    }
    
    is_super_admin = session.get('is_super_admin', 0)
    
    return render_template('admin_dashboard.html',
                         stats=stats,
                         users=users,
                         is_super_admin=is_super_admin)

@admin_bp.route('/admin/create-admin', methods=['POST'])
@super_admin_required
def create_admin():
    """Create a new admin (super admin only)"""
    username = request.form['username']
    email = request.form['email']
    password = request.form['password']
    
    user_id = create_user(username, password, email, role='admin', full_name=username)
    
    if user_id:
        flash('Admin created successfully!', 'success')
    else:
        flash('Failed to create admin. Username or email may already exist.', 'error')
    
    return redirect(url_for('admin.dashboard'))

@admin_bp.route('/admin/delete-user', methods=['POST'])
@admin_required
def delete_user_route():
    """Delete user (admin only)"""
    data = request.get_json()
    user_id = data.get('user_id')

    if user_id == 1:
        return jsonify({'success': False, 'message': 'Cannot delete default admin user'})

    if delete_user(user_id):
        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'message': 'Failed to delete user'})

@admin_bp.route('/admin/student-stats/<int:student_id>')
@admin_required
def get_student_stats(student_id):
    """Get student statistics"""
    stats = get_student_statistics(student_id)

    # Get detailed course statistics
    conn = get_db_connection()
    enrolled_courses = conn.execute('''
        SELECT c.id, c.name, c.required_attendance_percentage
        FROM courses c
        JOIN course_enrollments ce ON c.id = ce.course_id
        WHERE ce.student_id = ?
    ''', (student_id,)).fetchall()
    conn.close()

    course_stats = []
    for course in enrolled_courses:
        course_stat = get_student_course_statistics(student_id, course['id'])
        course_stats.append(course_stat)

    return jsonify({
        'enrolled_courses': stats['enrolled_courses'],
        'total_attended': stats['total_attended'],
        'courses': course_stats
    })

@admin_bp.route('/admin/course-management')
@admin_required
def course_management():
    """Course and student management page"""
    courses = get_all_courses()
    students = get_all_students_with_stats()  # Get students with statistics

    # Get enrollment data for each course
    course_data = []
    for course in courses:
        enrolled_students = get_course_students(course['id'])
        course_data.append({
            'course': course,
            'enrolled_count': len(enrolled_students),
            'enrolled_students': enrolled_students
        })

    return render_template('course_management.html',
                         course_data=course_data,
                         students=students,
                         all_courses=courses)

@admin_bp.route('/admin/create-course', methods=['POST'])
@admin_required
def create_course_route():
    """Create a new course"""
    name = request.form['name']
    description = request.form.get('description', '')
    course_code = request.form['course_code'].upper()
    start_date = request.form.get('start_date')
    end_date = request.form.get('end_date')
    days_per_week = int(request.form.get('days_per_week', 1))

    # Get selected days
    schedule_days = request.form.getlist('schedule_days')
    schedule_days_str = ','.join(schedule_days) if schedule_days else None

    required_attendance = int(request.form.get('required_attendance', 75))
    admin_id = session['user_id']

    course_id = create_course(name, description, course_code, admin_id,
                              start_date, end_date, days_per_week,
                              schedule_days_str, required_attendance)

    if course_id:
        flash(f'Course "{name}" created successfully!', 'success')
    else:
        flash('Course code already exists!', 'error')

    return redirect(url_for('admin.course_management'))

@admin_bp.route('/admin/delete-course', methods=['POST'])
@admin_required
def delete_course_route():
    """Delete a course"""
    data = request.get_json()
    course_id = data.get('course_id')
    
    if delete_course(course_id):
        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'message': 'Failed to delete course'})

@admin_bp.route('/admin/enroll-student', methods=['POST'])
@admin_required
def enroll_student_route():
    """Enroll a student in a course"""
    course_id = request.form['course_id']
    student_id = request.form['student_id']
    
    if enroll_student(course_id, student_id):
        flash('Student enrolled successfully!', 'success')
    else:
        flash('Failed to enroll student. Student may already be enrolled.', 'error')
    
    return redirect(url_for('admin.course_management'))

@admin_bp.route('/admin/unenroll-student', methods=['POST'])
@admin_required
def unenroll_student_route():
    """Unenroll a student from a course"""
    data = request.get_json()
    course_id = data.get('course_id')
    student_id = data.get('student_id')
    
    if unenroll_student(course_id, student_id):
        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'message': 'Failed to unenroll student'})

@admin_bp.route('/admin/course/<int:course_id>/sessions')
@admin_required
def course_sessions(course_id):
    """View and manage attendance sessions for a course"""
    course = get_course_by_id(course_id)
    if not course:
        flash('Course not found!', 'error')
        return redirect(url_for('admin.course_management'))
    
    sessions = get_course_sessions(course_id)
    enrolled_students = get_course_students(course_id)
    
    # Add attendance count to each session
    session_data = []
    for sess in sessions:
        attendance_records = get_session_attendance(sess['id'])
        session_data.append({
            'session': sess,
            'attendance_count': len(attendance_records),
            'attendance_records': attendance_records
        })
    
    return render_template('course_sessions.html',
                         course=course,
                         session_data=session_data,
                         enrolled_students=enrolled_students)

@admin_bp.route('/admin/create-session', methods=['POST'])
@admin_required
def create_session():
    """Create an attendance session with code"""
    course_id = request.form['course_id']
    session_name = request.form['session_name']
    session_date = request.form['session_date']
    code_type = request.form['code_type']  # 'manual' or 'auto'
    duration_minutes = int(request.form['duration_minutes'])
    
    # Generate or get code
    if code_type == 'auto':
        code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
    else:
        code = request.form['manual_code'].upper()
    
    # Calculate expiration time
    expires_at = datetime.now() + timedelta(minutes=duration_minutes)
    
    session_id = create_attendance_session(
        course_id, session_name, session_date, code, 
        code_type, expires_at, session['user_id']
    )
    
    if session_id:
        flash(f'Attendance session created! Code: {code}', 'success')
    else:
        flash('Failed to create session.', 'error')
    
    return redirect(url_for('admin.course_sessions', course_id=course_id))

@admin_bp.route('/admin/attendance-view')
@admin_required
def attendance_view():
    """View attendance records with filters"""
    courses = get_all_courses()
    
    # Get filter parameters
    selected_course = request.args.get('course_id', type=int)
    selected_date = request.args.get('date')
    
    attendance_records = []
    if selected_course and selected_date:
        attendance_records = get_course_attendance_by_date(selected_course, selected_date)
    
    return render_template('attendance_view.html',
                         courses=courses,
                         selected_course=selected_course,
                         selected_date=selected_date,
                         attendance_records=attendance_records)

