{% extends "base.html" %}

{% block title %}Mark Attendance - Attendance System{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card" style="max-width: 500px;">
        <div class="auth-header">
            <i class="fas fa-qrcode" style="font-size: 4rem;"></i>
            <h1>Mark Attendance</h1>
            <p>Enter the attendance code provided by your instructor</p>
        </div>
        
        <form method="POST" class="auth-form">
            <div class="form-group">
                <label for="code">
                    <i class="fas fa-key"></i>
                    Attendance Code
                </label>
                <input type="text" id="code" name="code" class="form-control" 
                       placeholder="Enter code (e.g., ABC123)" 
                       style="text-transform: uppercase; font-size: 1.5rem; text-align: center; letter-spacing: 3px;"
                       required autofocus>
            </div>
            
            <button type="submit" class="btn btn-primary auth-btn">
                <i class="fas fa-check"></i>
                Submit Code
            </button>
        </form>
        
        <div class="auth-footer">
            <p><a href="{{ url_for('student.dashboard') }}">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a></p>
            
            <div class="demo-credentials" style="background: #fff3cd; border-left-color: #ffc107;">
                <h6 style="color: #856404;"><i class="fas fa-info-circle"></i> Important</h6>
                <p style="color: #856404;">
                    • Codes are case-insensitive<br>
                    • Codes expire after a set time<br>
                    • You can only mark attendance once per session
                </p>
            </div>
        </div>
    </div>
    
    <div class="background-animation">
        <div class="floating-shape shape1"></div>
        <div class="floating-shape shape2"></div>
        <div class="floating-shape shape3"></div>
        <div class="floating-shape shape4"></div>
    </div>
</div>
{% endblock %}

