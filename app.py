from flask import Flask, redirect, url_for, session
from database import init_database

# Import blueprints
from login.routes import login_bp
from admin.routes import admin_bp
from student.routes import student_bp

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'

# Register blueprints
app.register_blueprint(login_bp)
app.register_blueprint(admin_bp)
app.register_blueprint(student_bp)

@app.route('/')
def index():
    """Home page - redirect to login or dashboard based on session"""
    if 'user_id' in session:
        if session.get('role') == 'admin':
            return redirect(url_for('admin.dashboard'))
        else:
            return redirect(url_for('student.dashboard'))
    return redirect(url_for('login.login'))



if __name__ == '__main__':
    init_database()
    app.run(debug=True)
