{% extends "base.html" %}

{% block title %}Student Dashboard - Attendance System{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Header -->
    <div class="dashboard-header">
        <div>
            <h1><i class="fas fa-user-graduate"></i> Student Dashboard</h1>
            <p>Welcome, {{ session.full_name }}</p>
        </div>
        <div style="display: flex; gap: 10px;">
            <a href="{{ url_for('student.mark_attendance_page') }}" class="btn btn-success">
                <i class="fas fa-qrcode"></i> Mark Attendance
            </a>
            <a href="{{ url_for('login.logout') }}" class="btn btn-danger">
                <i class="fas fa-sign-out-alt"></i> Logout
            </a>
        </div>
    </div>

    <!-- Quick Action -->
    <div style="margin-bottom: 30px; text-align: center;">
        <a href="{{ url_for('student.mark_attendance_page') }}" class="btn btn-primary btn-lg" style="padding: 20px 40px; font-size: 1.2rem;">
            <i class="fas fa-qrcode"></i> Enter Attendance Code
        </a>
    </div>

    <!-- Enrolled Courses -->
    <div class="dashboard-section">
        <div class="section-header">
            <h2><i class="fas fa-book"></i> My Enrolled Courses</h2>
            <span class="badge bg-info" style="font-size: 1.1rem; padding: 10px 15px;">
                {{ courses|length }} courses
            </span>
        </div>
        <div style="padding: 20px;">
            {% if courses %}
            <div class="row">
                {% for course in courses %}
                <div class="col-md-6 mb-3">
                    <div class="card" style="border-left: 4px solid #667eea; cursor: pointer;"
                         onclick="showCourseStats({{ course.id }})">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-book-open"></i> {{ course.name }}
                                <i class="fas fa-chart-line float-end text-muted" style="font-size: 0.9rem;"></i>
                            </h5>
                            <p class="card-text">
                                <strong>Course Code:</strong> {{ course.course_code }}<br>
                                <strong>Description:</strong> {{ course.description or 'N/A' }}<br>
                                <small class="text-muted">Enrolled on: {{ course.enrolled_at[:10] }}</small>
                            </p>
                            <small class="text-info"><i class="fas fa-info-circle"></i> Click to view attendance details</small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> You are not enrolled in any courses yet. Please contact your administrator.
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Instructions -->
    <div class="dashboard-section">
        <div class="section-header">
            <h2><i class="fas fa-question-circle"></i> How to Mark Attendance</h2>
        </div>
        <div style="padding: 20px;">
            <ol style="font-size: 1.1rem; line-height: 2;">
                <li>Click the "Mark Attendance" button above</li>
                <li>Enter the attendance code provided by your instructor</li>
                <li>Submit the code to mark your attendance</li>
                <li>You will receive a confirmation message</li>
            </ol>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> <strong>Note:</strong> Attendance codes expire after a certain time. Make sure to enter the code before it expires!
            </div>
        </div>
    </div>
</div>

<!-- Course Stats Modal -->
<div class="modal fade" id="courseStatsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-chart-bar"></i> Course Attendance Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="courseStatsContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showCourseStats(courseId) {
    const modal = new bootstrap.Modal(document.getElementById('courseStatsModal'));
    const content = document.getElementById('courseStatsContent');

    // Show loading
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    `;

    modal.show();

    // Fetch course stats
    fetch('/student/course-stats/' + courseId)
        .then(response => response.json())
        .then(data => {
            const percentage = data.attendance_percentage;
            const required = data.required_percentage;
            const status = percentage >= required ? 'success' : 'danger';
            const statusText = percentage >= required ? 'Good Standing' : 'Below Required';

            let html = `
                <h4 class="mb-3">${data.course_name}</h4>
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center border-primary">
                            <div class="card-body">
                                <h3 class="text-primary">${data.total_sessions}</h3>
                                <p class="mb-0 small">Total Sessions</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center border-success">
                            <div class="card-body">
                                <h3 class="text-success">${data.attended_sessions}</h3>
                                <p class="mb-0 small">Attended</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center border-${status}">
                            <div class="card-body">
                                <h3 class="text-${status}">${percentage.toFixed(1)}%</h3>
                                <p class="mb-0 small">Your Attendance</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center border-warning">
                            <div class="card-body">
                                <h3 class="text-warning">${required}%</h3>
                                <p class="mb-0 small">Required</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-${status}">
                    <h5><i class="fas fa-${percentage >= required ? 'check-circle' : 'exclamation-triangle'}"></i> Status: ${statusText}</h5>
                    <p class="mb-0">
                        ${percentage >= required ?
                            'Great job! You are meeting the attendance requirements.' :
                            `You need to attend ${Math.ceil((required * data.total_sessions / 100) - data.attended_sessions)} more session(s) to meet the requirement.`
                        }
                    </p>
                </div>

                <div class="progress" style="height: 30px;">
                    <div class="progress-bar bg-${status}" role="progressbar"
                         style="width: ${percentage}%;"
                         aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100">
                        ${percentage.toFixed(1)}%
                    </div>
                </div>
                <div class="d-flex justify-content-between mt-2">
                    <small>0%</small>
                    <small class="text-warning">Required: ${required}%</small>
                    <small>100%</small>
                </div>
            `;

            content.innerHTML = html;
        })
        .catch(error => {
            content.innerHTML = '<div class="alert alert-danger">Error loading course statistics</div>';
        });
}
</script>
{% endblock %}

