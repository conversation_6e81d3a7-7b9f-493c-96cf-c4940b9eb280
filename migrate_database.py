"""
Database migration script to add new columns to courses table
"""
import sqlite3
import os

def migrate_database():
    """Add new columns to existing database"""
    db_path = 'attendance.db'
    
    if not os.path.exists(db_path):
        print("Database doesn't exist. Run app.py to create it.")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check if columns already exist
    cursor.execute("PRAGMA table_info(courses)")
    columns = [column[1] for column in cursor.fetchall()]
    
    # Add new columns if they don't exist
    new_columns = [
        ('start_date', 'DATE'),
        ('end_date', 'DATE'),
        ('days_per_week', 'INTEGER DEFAULT 1'),
        ('schedule_days', 'TEXT'),
        ('required_attendance_percentage', 'INTEGER DEFAULT 75')
    ]
    
    for column_name, column_type in new_columns:
        if column_name not in columns:
            try:
                cursor.execute(f'ALTER TABLE courses ADD COLUMN {column_name} {column_type}')
                print(f"✓ Added column: {column_name}")
            except sqlite3.OperationalError as e:
                print(f"✗ Error adding {column_name}: {e}")
    
    conn.commit()
    conn.close()
    print("\n✓ Database migration completed!")

if __name__ == '__main__':
    migrate_database()

