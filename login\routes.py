from flask import Blueprint, render_template, request, redirect, url_for, session, flash
import sqlite3
from database import (
    get_user_by_username, get_user_by_email, create_user,
    verify_password, get_db_connection
)

login_bp = Blueprint('login', __name__)

@login_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Login page - accepts username or email"""
    if request.method == 'POST':
        identifier = request.form['identifier']  # Can be username or email
        password = request.form['password']
        
        # Try to find user by username first, then by email
        user = get_user_by_username(identifier)
        if not user:
            user = get_user_by_email(identifier)
        
        if user and verify_password(password, user['password']):
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['full_name'] = user['full_name'] or user['username']
            session['role'] = user['role']
            session['is_super_admin'] = user['is_super_admin']
            
            flash('Login successful!', 'success')
            
            if user['role'] == 'admin':
                return redirect(url_for('admin.dashboard'))
            else:
                return redirect(url_for('student.dashboard'))
        else:
            flash('Invalid username/email or password!', 'error')
    
    return render_template('login.html')

@login_bp.route('/register', methods=['GET', 'POST'])
def register():
    """Registration page - simplified without full_name field"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        confirm_password = request.form['confirm_password']
        email = request.form['email']
        
        # Validation
        if password != confirm_password:
            flash('Passwords do not match!', 'error')
            return render_template('register.html')

        if len(password) < 6:
            flash('Password must be at least 6 characters long!', 'error')
            return render_template('register.html')

        # Check if username already exists
        if get_user_by_username(username):
            flash('Username already exists!', 'error')
            return render_template('register.html')

        # Check if email already exists
        if get_user_by_email(email):
            flash('Email already exists!', 'error')
            return render_template('register.html')
        
        # Create new user (role defaults to 'student', full_name defaults to username)
        user_id = create_user(username, password, email, role='student')
        
        if user_id:
            flash('Registration successful! Please login.', 'success')
            return redirect(url_for('login.login'))
        else:
            flash('Registration failed! Please try again.', 'error')
    
    return render_template('register.html')

@login_bp.route('/logout')
def logout():
    """Logout and clear session"""
    session.clear()
    flash('You have been logged out.', 'info')
    return redirect(url_for('login.login'))

